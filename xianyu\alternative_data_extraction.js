/**
 * 替代方案：直接从页面DOM提取数据
 * 当API监听失败时使用此方案
 */

/**
 * 从页面DOM直接提取商品数据
 */
function extractDataFromDOM() {
    console.log("=== 开始从DOM提取数据 ===");
    
    let products = [];
    
    // 查找商品卡片元素（需要根据实际页面结构调整选择器）
    const productSelectors = [
        '.item-card',           // 可能的商品卡片类名
        '.product-item',        // 可能的商品项类名
        '[data-id]',           // 包含data-id属性的元素
        '.card-item',          // 卡片项
        '.list-item'           // 列表项
    ];
    
    let productElements = [];
    
    // 尝试不同的选择器
    for (let selector of productSelectors) {
        let elements = document.querySelectorAll(selector);
        if (elements.length > 0) {
            console.log(`使用选择器 ${selector} 找到 ${elements.length} 个元素`);
            productElements = elements;
            break;
        }
    }
    
    if (productElements.length === 0) {
        console.log("未找到商品元素，尝试通用方法...");
        productElements = findProductElementsGeneric();
    }
    
    // 提取每个商品的数据
    productElements.forEach((element, index) => {
        try {
            let product = extractSingleProductFromDOM(element);
            if (product && product.id) {
                products.push(product);
                console.log(`提取商品 ${index + 1}: ${product.title}`);
            }
        } catch (error) {
            console.error(`提取商品 ${index + 1} 失败:`, error);
        }
    });
    
    console.log(`DOM提取完成，共获取 ${products.length} 个商品`);
    return products;
}

/**
 * 通用方法查找商品元素
 */
function findProductElementsGeneric() {
    // 查找包含价格信息的元素
    let priceElements = document.querySelectorAll('*');
    let productElements = [];
    
    priceElements.forEach(element => {
        let text = element.textContent || '';
        // 查找包含价格模式的元素（如：￥123、123元）
        if (/[￥¥]\s*\d+|^\d+\s*元/.test(text)) {
            // 向上查找可能的商品容器
            let container = element.closest('div, li, article, section');
            if (container && !productElements.includes(container)) {
                productElements.push(container);
            }
        }
    });
    
    return productElements;
}

/**
 * 从单个DOM元素提取商品数据
 */
function extractSingleProductFromDOM(element) {
    let product = {
        id: '',
        title: '',
        price: 0,
        image: '',
        url: '',
        want_count: 0,
        bargain_count: 0,
        hot_rank: null
    };
    
    try {
        // 提取商品ID
        product.id = element.getAttribute('data-id') || 
                    element.getAttribute('id') || 
                    extractIdFromUrl(element);
        
        // 提取标题
        let titleElement = element.querySelector('h1, h2, h3, h4, .title, .name, [class*="title"]');
        if (titleElement) {
            product.title = titleElement.textContent.trim();
        }
        
        // 提取价格
        let priceElement = element.querySelector('.price, [class*="price"], [class*="money"]');
        if (priceElement) {
            let priceText = priceElement.textContent;
            let priceMatch = priceText.match(/[\d.]+/);
            if (priceMatch) {
                product.price = parseFloat(priceMatch[0]);
            }
        }
        
        // 提取图片
        let imgElement = element.querySelector('img');
        if (imgElement) {
            product.image = imgElement.src || imgElement.getAttribute('data-src');
        }
        
        // 提取链接
        let linkElement = element.querySelector('a');
        if (linkElement) {
            product.url = linkElement.href;
        }
        
        // 提取标签信息
        let labelElements = element.querySelectorAll('.label, .tag, [class*="label"], [class*="tag"]');
        labelElements.forEach(label => {
            let text = label.textContent;
            
            // 提取想要人数
            let wantMatch = text.match(/(\d+)人想要/);
            if (wantMatch) {
                product.want_count = parseInt(wantMatch[1]);
            }
            
            // 提取小刀价人数
            let bargainMatch = text.match(/(\d+)人小刀价/);
            if (bargainMatch) {
                product.bargain_count = parseInt(bargainMatch[1]);
            }
            
            // 提取热销排名
            let hotMatch = text.match(/热销第(\d+)名/);
            if (hotMatch) {
                product.hot_rank = parseInt(hotMatch[1]);
            }
        });
        
        return product;
        
    } catch (error) {
        console.error('提取单个商品数据失败:', error);
        return null;
    }
}

/**
 * 从URL中提取商品ID
 */
function extractIdFromUrl(element) {
    let links = element.querySelectorAll('a');
    for (let link of links) {
        let href = link.href;
        if (href) {
            // 尝试从URL中提取ID
            let idMatch = href.match(/id[=\/](\d+)/i) || 
                         href.match(/item[=\/](\d+)/i) ||
                         href.match(/(\d{8,})/); // 8位以上数字
            if (idMatch) {
                return idMatch[1];
            }
        }
    }
    return '';
}

/**
 * 滚动页面并提取数据
 */
function scrollAndExtractData() {
    console.log("=== 开始滚动提取数据 ===");
    
    let allProducts = [];
    let scrollCount = 0;
    let maxScrolls = 20;
    let lastProductCount = 0;
    
    function doScroll() {
        // 滚动到底部
        window.scrollTo(0, document.body.scrollHeight);
        
        // 等待加载
        setTimeout(() => {
            // 提取当前页面的商品数据
            let currentProducts = extractDataFromDOM();
            
            // 去重合并
            currentProducts.forEach(product => {
                if (!allProducts.find(p => p.id === product.id)) {
                    allProducts.push(product);
                }
            });
            
            console.log(`第 ${scrollCount + 1} 次滚动，当前共 ${allProducts.length} 个商品`);
            
            scrollCount++;
            
            // 检查是否需要继续滚动
            if (scrollCount < maxScrolls && allProducts.length > lastProductCount) {
                lastProductCount = allProducts.length;
                doScroll();
            } else {
                console.log("滚动提取完成");
                console.log(`最终获取 ${allProducts.length} 个商品`);
                
                // 调用Python函数处理数据
                processExtractedData(allProducts);
            }
        }, 2000);
    }
    
    doScroll();
}

/**
 * 处理提取的数据
 */
function processExtractedData(products) {
    console.log("=== 处理提取的数据 ===");
    
    // 转换为API格式
    let apiFormatData = [{
        api: "dom_extraction",
        data: {
            cardList: products.map(product => ({
                cardData: {
                    id: product.id,
                    title: product.title,
                    priceInfo: { price: product.price.toString() },
                    detailUrl: product.url,
                    picInfo: { picUrl: product.image },
                    itemLabelDataVO: {
                        labelData: {
                            r3: []
                        }
                    }
                }
            }))
        }
    }];
    
    // 添加标签信息
    products.forEach((product, index) => {
        let cardData = apiFormatData[0].data.cardList[index].cardData;
        
        if (product.want_count > 0) {
            cardData.itemLabelDataVO.labelData.r3.push({
                content: `${product.want_count}人想要`,
                labelId: 9
            });
        }
        
        if (product.bargain_count > 0) {
            cardData.itemLabelDataVO.labelData.r3.push({
                content: `${product.bargain_count}人小刀价`,
                labelId: 1047
            });
        }
        
        if (product.hot_rank) {
            if (!cardData.itemLabelDataVO.labelData.r2) {
                cardData.itemLabelDataVO.labelData.r2 = [];
            }
            cardData.itemLabelDataVO.labelData.r2.push({
                content: `热销第${product.hot_rank}名`,
                labelId: 1291
            });
        }
    });
    
    // 调用Python函数
    try {
        let result = callPythonFunction('filter_product_list', {
            data: apiFormatData,
            filters: {
                want_count_min: 5,
                price_max: 100
            }
        });
        
        console.log("Python处理结果:", result);
        
    } catch (error) {
        console.error("调用Python函数失败:", error);
    }
}

// 导出函数
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        extractDataFromDOM,
        scrollAndExtractData,
        processExtractedData
    };
}

console.log("DOM提取脚本已加载，可以调用：");
console.log("1. extractDataFromDOM() - 提取当前页面数据");
console.log("2. scrollAndExtractData() - 滚动并提取所有数据");
