/**
 * 影刀调试脚本 - 用于发现实际的API接口名称
 * 使用方法：在闲鱼页面运行此脚本，然后手动滚动页面，查看控制台输出
 */

// 调试配置
const DEBUG_CONFIG = {
    MONITOR_TIME: 30000,  // 监听30秒
    LOG_ALL_REQUESTS: true // 记录所有请求
};

/**
 * 主调试函数
 */
function debugApiMonitor() {
    console.log("=== 开始API调试监听 ===");
    console.log("请在30秒内手动滚动页面，观察API调用");
    
    // 方法1：使用影刀的网络监听功能
    monitorWithYingdao();
    
    // 方法2：使用浏览器原生监听
    monitorWithBrowser();
    
    // 30秒后停止监听
    setTimeout(() => {
        console.log("=== API调试监听结束 ===");
    }, DEBUG_CONFIG.MONITOR_TIME);
}

/**
 * 方法1：使用影刀的网络监听
 */
function monitorWithYingdao() {
    console.log("--- 方法1：影刀网络监听 ---");
    
    // 监听所有可能的闲鱼API
    const possibleApis = [
        'mtop.idle.web.xyh.item.list',
        'mtop.idle.web.xyh.item.list.2.0',
        'mtop.taobao.idle.home.list',
        'mtop.taobao.idle.item.list',
        'item.list',
        'idle.web',
        'xyh.item',
        'cardList'
    ];
    
    possibleApis.forEach(api => {
        try {
            // 尝试监听每个可能的API
            let response = interceptApiResponse(api);
            if (response) {
                console.log(`✅ 发现API: ${api}`);
                console.log("响应数据:", JSON.stringify(response, null, 2));
            }
        } catch (error) {
            // 忽略错误，继续尝试其他API
        }
    });
}

/**
 * 方法2：使用浏览器原生监听
 */
function monitorWithBrowser() {
    console.log("--- 方法2：浏览器原生监听 ---");
    
    // 监听所有XHR请求
    const originalXHROpen = XMLHttpRequest.prototype.open;
    const originalXHRSend = XMLHttpRequest.prototype.send;
    
    XMLHttpRequest.prototype.open = function(method, url, ...args) {
        this._method = method;
        this._url = url;
        return originalXHROpen.apply(this, [method, url, ...args]);
    };
    
    XMLHttpRequest.prototype.send = function(data) {
        const xhr = this;
        
        xhr.addEventListener('load', function() {
            if (xhr._url && (xhr._url.includes('idle') || xhr._url.includes('mtop'))) {
                console.log(`🔍 XHR请求: ${xhr._method} ${xhr._url}`);
                
                try {
                    const response = JSON.parse(xhr.responseText);
                    if (response.data && response.data.cardList) {
                        console.log("✅ 找到商品列表数据!");
                        console.log("URL:", xhr._url);
                        console.log("商品数量:", response.data.cardList.length);
                        console.log("响应示例:", JSON.stringify(response.data.cardList[0], null, 2));
                    }
                } catch (e) {
                    // 忽略JSON解析错误
                }
            }
        });
        
        return originalXHRSend.apply(this, arguments);
    };
    
    // 监听Fetch请求
    const originalFetch = window.fetch;
    window.fetch = function(url, options = {}) {
        if (typeof url === 'string' && (url.includes('idle') || url.includes('mtop'))) {
            console.log(`🔍 Fetch请求: ${options.method || 'GET'} ${url}`);
        }
        
        return originalFetch.apply(this, arguments).then(response => {
            if (typeof url === 'string' && (url.includes('idle') || url.includes('mtop'))) {
                response.clone().json().then(data => {
                    if (data.data && data.data.cardList) {
                        console.log("✅ 找到商品列表数据!");
                        console.log("URL:", url);
                        console.log("商品数量:", data.data.cardList.length);
                    }
                }).catch(() => {
                    // 忽略JSON解析错误
                });
            }
            return response;
        });
    };
}

/**
 * 简化版监听函数 - 只监听包含特定关键词的请求
 */
function simpleApiMonitor() {
    console.log("=== 简化版API监听 ===");
    console.log("监听包含 'idle'、'item'、'list' 关键词的所有请求");
    
    // 监听所有网络请求
    const observer = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
            if (entry.name.includes('idle') || 
                entry.name.includes('item') || 
                entry.name.includes('list')) {
                console.log(`🔍 网络请求: ${entry.name}`);
            }
        }
    });
    
    observer.observe({ entryTypes: ['resource'] });
    
    // 30秒后停止监听
    setTimeout(() => {
        observer.disconnect();
        console.log("监听结束");
    }, DEBUG_CONFIG.MONITOR_TIME);
}

/**
 * 检查页面中的数据
 */
function checkPageData() {
    console.log("=== 检查页面数据 ===");
    
    // 检查window对象中可能存在的数据
    for (let key in window) {
        if (key.includes('idle') || key.includes('data') || key.includes('card')) {
            console.log(`发现window属性: ${key}`, window[key]);
        }
    }
    
    // 检查页面中的script标签
    const scripts = document.querySelectorAll('script');
    scripts.forEach((script, index) => {
        if (script.textContent.includes('cardList') || 
            script.textContent.includes('idle') ||
            script.textContent.includes('itemList')) {
            console.log(`发现相关script标签 ${index}:`, script.textContent.substring(0, 200));
        }
    });
}

/**
 * 影刀专用：获取所有网络请求
 */
function getAllNetworkRequests() {
    console.log("=== 获取所有网络请求 ===");
    
    try {
        // 使用影刀的网络监听功能
        let allRequests = getAllApiResponses(); // 假设影刀提供此函数
        
        if (allRequests && allRequests.length > 0) {
            console.log(`共发现 ${allRequests.length} 个请求`);
            
            allRequests.forEach((request, index) => {
                if (request.url && (request.url.includes('idle') || request.url.includes('mtop'))) {
                    console.log(`请求 ${index}: ${request.url}`);
                    
                    if (request.response && request.response.data && request.response.data.cardList) {
                        console.log("✅ 这个请求包含商品列表数据!");
                    }
                }
            });
        } else {
            console.log("未获取到网络请求数据");
        }
    } catch (error) {
        console.log("获取网络请求失败:", error.message);
    }
}

// 导出调试函数
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        debugApiMonitor,
        simpleApiMonitor,
        checkPageData,
        getAllNetworkRequests
    };
}

// 自动运行调试
console.log("调试脚本已加载，可以调用以下函数：");
console.log("1. debugApiMonitor() - 完整调试");
console.log("2. simpleApiMonitor() - 简化监听");
console.log("3. checkPageData() - 检查页面数据");
console.log("4. getAllNetworkRequests() - 获取所有请求");
