# 闲鱼数据分析工具

一个基于影刀RPA和Python的闲鱼热销商品数据分析工具，可以自动获取、筛选和分析闲鱼商品数据，并导出Excel报告。

## ✨ 主要功能

- 🔍 **自动数据获取**：通过影刀RPA自动获取闲鱼API数据
- 📊 **智能数据筛选**：支持多维度筛选条件（价格、想要人数、热销排名、发布时间等）
- 📈 **热销商品分析**：识别热销排名、想要人数、小刀价等关键指标
- ⏰ **时间筛选**：支持按发布时间范围筛选（24/48/72小时内）
- 📋 **Excel导出**：自动生成格式化的Excel分析报告
- 🎯 **关键词过滤**：支持包含/排除关键词筛选

## 🚀 快速开始

### 1. 环境准备

```bash
# 安装Python依赖
pip install pandas openpyxl

# 安装影刀RPA软件（从官网下载）
```

### 2. 使用影刀数据处理（推荐）

```python
from xianyu_analyzer import process_yingdao_response

# 影刀只需要传递response_body_list数据
# 筛选条件和输出文件名已在代码中预设
result = process_yingdao_response(response_body_list=yingdao_data)

print(f"筛选出 {result['count']} 个商品")
print(f"Excel文件：{result['file']}")
```

**预设的筛选条件**：
- 至少50人想要
- 价格100-20000元
- 热销排名前10名
- 72小时内发布
- 包含关键词：手机、iPhone、华为、小米、OPPO、vivo
- 排除关键词：坏了、故障、维修、翻新

### 3. 影刀配置

在影刀中设置API监控：
- **监控模式**：API监控
- **URL模式**：`*mtop.idle.web.xyh.item.list*`
- **获取数据**：`response_body_list`

## 📁 文件说明

| 文件 | 说明 |
|------|------|
| `xianyu_analyzer.py` | 核心分析模块 |
| `test_yingdao_processing.py` | 测试脚本 |
| `yingdao_usage_example.py` | 使用示例 |
| `使用说明.md` | 详细使用说明 |
| `闲鱼数据分析工具设计方案.md` | 设计文档 |

## 📊 输出字段

Excel文件包含以下字段：

| 字段 | 说明 |
|------|------|
| 商品ID | 商品唯一标识 |
| 商品标题 | 商品名称 |
| 商品价格 | 商品价格（元） |
| 发布时间范围 | 如"48小时内发布" |
| 想要人数 | 多少人想要这个商品 |
| 小刀价人数 | 多少人出了小刀价 |
| 热销排名 | 在热销榜中的排名 |
| 商品链接 | 商品详情页链接 |
| 主图URL | 商品主图链接 |
| 排名分数 | 商品的排名分数 |

## 🔧 修改筛选条件

如需修改筛选条件，请编辑 `xianyu_analyzer.py` 文件中 `process_yingdao_response` 函数的 `filters` 变量：

```python
# 在 xianyu_analyzer.py 中修改这些预设值
filters = {
    'want_count_min': 50,        # 最少想要人数
    'price_min': 100,            # 最低价格
    'price_max': 20000,          # 最高价格
    'hot_rank_max': 10,          # 热销排名（1-10名）
    'publish_hours_max': 72,     # 发布时间范围（小时）
    'keywords': ['手机', 'iPhone', '华为', '小米', 'OPPO', 'vivo'],
    'exclude_keywords': ['坏了', '故障', '维修', '翻新']
}
```

## 🧪 测试

运行测试脚本验证功能：

```bash
# 运行测试
python test_yingdao_processing.py

# 查看使用示例
python yingdao_usage_example.py
```

## 📝 使用流程

1. **配置影刀**：设置API监控，获取闲鱼商品列表数据
2. **设置筛选条件**：根据需求配置价格、排名、时间等筛选条件
3. **处理数据**：调用`process_yingdao_response`函数处理数据
4. **查看结果**：检查生成的Excel文件和筛选结果

## ⚠️ 注意事项

- 请遵守闲鱼平台的使用条款和robots.txt规定
- 建议设置合理的请求间隔，避免对服务器造成过大压力
- 数据仅供学习和研究使用，请勿用于商业用途
- 使用前请在测试环境中验证功能

## 📞 技术支持

如遇到问题，请检查：
1. Python环境和依赖库是否正确安装
2. 影刀配置是否正确
3. API监控是否成功获取数据
4. 筛选条件设置是否合理

---

*最后更新：2025-08-02*
