# 闲鱼数据分析工具使用说明

## 📋 项目概述

本工具采用**影刀RPA + Python**的两步法方案，用于分析闲鱼店铺的热销产品数据。

### 核心功能
- ✅ 自动获取商品列表数据
- ✅ 智能筛选目标商品
- ✅ 获取商品详情（包含发布时间）
- ✅ 根据24/48/72小时筛选
- ✅ 导出Excel分析报告

## 🗂️ 文件结构

```
xianyu/
├── xianyu_analyzer.py          # Python核心分析模块
├── yingdao_example.js          # 影刀脚本示例
├── 使用说明.md                 # 本文件
├── 闲鱼数据分析工具设计方案.md   # 详细设计文档
├── 接口响应详情分析.md          # API响应数据分析
└── 商品详情页.md               # 详情页API分析
```

## 🚀 快速开始

### 1. 环境准备

**Python环境**：
```bash
pip install pandas openpyxl
```

**影刀环境**：
- 安装影刀RPA软件
- 配置Python函数调用环境

### 2. 配置筛选条件

编辑 `yingdao_example.js` 中的筛选条件：

```javascript
const FILTERS = {
    // 第一步筛选条件（商品列表页）
    STEP1: {
        want_count_min: 5,           // 至少5人想要
        price_max: 100,              // 价格不超过100元
        price_min: 1,                // 价格不低于1元
        keywords: ["教材", "英语"],   // 包含关键词
        exclude_keywords: ["二手"]    // 排除关键词
    },
    
    // 第二步筛选条件（商品详情页）
    STEP2: {
        publish_hours: 24,           // 24小时内发布
        want_count_min: 5,           // 至少5人想要
        price_max: 100,              // 价格不超过100元
        keywords: ["教材", "英语"]
    }
};
```

### 3. 运行流程

1. **打开闲鱼店铺页面**
2. **运行影刀脚本**：调用 `analyzeXianyuProducts()` 函数
3. **等待自动执行**：
   - 第一步：滚动获取商品列表，初步筛选
   - 第二步：点击目标商品，获取详情，精确筛选
4. **查看结果**：Excel文件自动保存到当前目录

## 📊 输出结果

### Excel文件字段说明

| 列 | 字段名 | 说明 |
|----|--------|------|
| A | 商品ID | 商品唯一标识 |
| B | 商品标题 | 商品标题 |
| C | 商品价格 | 商品价格(元) |
| D | 发布时间 | 商品发布时间 |
| E | 店铺名 | 店铺昵称 |
| F | 地区 | 卖家地区 |
| G | 想要人数 | "X人想要"中的数字 |
| H | 小刀价人数 | "X人小刀价"中的数字 |
| I | 热销排名 | "热销第X名"中的数字 |
| J | 商品描述 | 商品详细描述（截取前100字符） |
| K | 商品链接 | 商品详情页链接 |
| L | 主图URL | 商品主图链接 |

### 文件命名规则
```
闲鱼热销产品_YYYYMMDD_HHMM.xlsx
例如：闲鱼热销产品_20250802_1430.xlsx
```

## ⚙️ 高级配置

### 1. 调整等待时间

在 `yingdao_example.js` 中修改：

```javascript
const CONFIG = {
    SCROLL_WAIT_TIME: 2000,      // 滚动等待时间(毫秒)
    DETAIL_WAIT_TIME: 3000,      // 详情页加载等待时间(毫秒)
    CLICK_WAIT_TIME: 1000,       // 点击等待时间(毫秒)
    MAX_SCROLL_ATTEMPTS: 50,     // 最大滚动次数
    BATCH_SIZE: 10               // 批量处理详情页数量
};
```

### 2. 自定义筛选条件

**时间筛选**：
```javascript
publish_hours: 24    // 24小时内发布
publish_hours: 48    // 48小时内发布
publish_hours: 72    // 72小时内发布
```

**价格筛选**：
```javascript
price_min: 10,       // 最低价格10元
price_max: 200,      // 最高价格200元
```

**关键词筛选**：
```javascript
keywords: ["教材", "英语", "学习"],           // 必须包含的关键词
exclude_keywords: ["二手", "破损", "缺页"]    // 必须排除的关键词
```

**热销排名筛选**：
```javascript
hot_rank_max: 10     // 只要热销前10名的商品
```

## 🔧 影刀配置要点

### 1. API拦截设置

需要拦截以下两个API接口：

**商品列表API**：
```
mtop.idle.web.xyh.item.list
```

**商品详情API**：
```
mtop.taobao.idle.item.web.recommend.list
```

### 2. 页面操作设置

**滚动操作**：
- 使用 `scrollToBottom()` 滚动到页面底部
- 每次滚动后等待2秒加载数据

**点击操作**：
- 根据商品ID查找商品元素
- 点击后等待3秒加载详情页
- 获取数据后关闭详情页返回列表

### 3. 错误处理

- 设置最大滚动次数限制
- 处理点击失败的情况
- 处理API拦截失败的情况
- 自动关闭异常打开的标签页

## 🐛 常见问题

### Q1: 获取不到API数据怎么办？
**A**: 检查API拦截设置，确保拦截的接口名称正确，等待时间足够。

### Q2: 点击商品进入详情页失败？
**A**: 检查 `findElementByProductId()` 函数的元素查找逻辑，根据实际页面结构调整。

### Q3: Excel文件导出失败？
**A**: 检查Python环境是否安装了 `pandas` 和 `openpyxl` 库。

### Q4: 筛选结果为空？
**A**: 检查筛选条件是否过于严格，可以适当放宽条件进行测试。

### Q5: 脚本运行速度太慢？
**A**: 可以适当减少等待时间，但要确保页面加载完成。

## 📝 测试建议

### 1. 小规模测试
- 先设置较小的 `MAX_SCROLL_ATTEMPTS`（如5次）
- 测试基本功能是否正常

### 2. 逐步调试
- 先测试第一步商品列表获取
- 再测试第二步商品详情获取
- 最后测试完整流程

### 3. 数据验证
- 检查获取的商品数量是否合理
- 验证发布时间筛选是否准确
- 确认Excel导出数据的完整性

## 📞 技术支持

如遇到问题，请检查：
1. 影刀脚本的API拦截配置
2. Python环境和依赖库安装
3. 筛选条件设置是否合理
4. 页面元素查找逻辑是否正确

建议在测试环境中先进行小规模验证，确认无误后再进行大规模数据采集。

## 🎯 影刀数据直接处理（推荐方式）

### 简化的单步处理方法

如果你已经通过影刀获取了API响应数据，可以直接使用 `process_yingdao_response` 函数进行处理：

#### 1. 使用示例

```python
from xianyu_analyzer import process_yingdao_response

# 影刀返回的response_body_list数据
response_data = [
    {
        "type": "XHR",
        "url": "https://h5api.m.taobao.com/h5/mtop.idle.web.xyh.item.list/1.0/...",
        "status": 200,
        "headers": {...},
        "body": "{\"api\":\"mtop.idle.web.xyh.item.list\",\"data\":{\"cardList\":[...]}}"
    }
]

# 设置筛选条件
filters = {
    'want_count_min': 50,        # 至少50人想要
    'price_min': 100,            # 最低价格100元
    'price_max': 20000,          # 最高价格20000元
    'hot_rank_max': 10,          # 热销排名前10名
    'publish_hours_max': 72,     # 72小时内发布
    'keywords': ['手机', 'iPhone'],  # 包含关键词
    'exclude_keywords': ['坏了', '故障']  # 排除关键词
}

# 处理数据并导出Excel
result = process_yingdao_response(
    response_body_list=response_data,
    filters=filters,
    output_file='闲鱼热销商品.xlsx'
)

print(f"筛选出 {result['count']} 个商品")
print(f"Excel文件：{result['file']}")
```

#### 2. 影刀配置

在影刀中配置API监控：
- **监控模式**：API监控
- **URL模式**：`*mtop.idle.web.xyh.item.list*`
- **获取数据**：`response_body_list`

#### 3. 输出字段说明

Excel文件包含以下字段：
- **商品ID**：商品唯一标识
- **商品标题**：商品名称
- **商品价格**：商品价格（元）
- **发布时间范围**：如"48小时内发布"
- **想要人数**：多少人想要这个商品
- **小刀价人数**：多少人出了小刀价
- **热销排名**：在热销榜中的排名
- **商品链接**：商品详情页链接
- **主图URL**：商品主图链接
- **排名分数**：商品的排名分数

#### 4. 测试示例

运行测试脚本验证功能：
```bash
python test_yingdao_processing.py
```

或查看使用示例：
```bash
python yingdao_usage_example.py
```

---

*最后更新：2025-08-02*
