#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
影刀数据处理使用示例
这个脚本展示如何使用 xianyu_analyzer 处理影刀返回的数据
"""

from xianyu_analyzer import process_yingdao_response

def main():
    """
    主函数：演示如何处理影刀返回的数据
    
    使用步骤：
    1. 在影刀中配置API监控，获取response_body_list数据
    2. 将数据传递给process_yingdao_response函数
    3. 设置筛选条件
    4. 获取处理结果和Excel文件
    """
    
    # 示例：从影刀获取的response_body_list数据
    # 实际使用时，这个数据来自影刀的API监控结果
    response_body_list = [
        # 这里应该是影刀返回的实际数据
        # 格式如下：
        # {
        #     "type": "XHR",
        #     "url": "https://h5api.m.taobao.com/h5/mtop.idle.web.xyh.item.list/1.0/...",
        #     "status": 200,
        #     "headers": {...},
        #     "body": "{\"api\":\"mtop.idle.web.xyh.item.list\",\"data\":{\"cardList\":[...]}}"
        # }
    ]
    
    # 处理数据并导出Excel（筛选条件和输出文件名已在函数内预设）
    try:
        result = process_yingdao_response(response_body_list=response_body_list)
        
        if result['success']:
            print(f"✅ 数据处理成功！")
            print(f"📊 筛选出 {result['count']} 个符合条件的商品")
            print(f"📁 Excel文件已保存：{result['file']}")
            
            # 显示前几个商品的预览
            if result['products']:
                print(f"\n📋 商品预览（前{len(result['products'])}个）：")
                for i, product in enumerate(result['products'], 1):
                    print(f"\n{i}. {product['title']}")
                    print(f"   💰 价格：{product['price']}元")
                    print(f"   ❤️  想要：{product['want_count']}人")
                    print(f"   🔥 排名：第{product['hot_rank']}名" if product['hot_rank'] else "   🔥 排名：未上榜")
                    print(f"   ⏰ 发布：{product['publish_within_hours']}小时内" if product['publish_within_hours'] else "   ⏰ 发布：时间未知")
        else:
            print("❌ 数据处理失败")
            
    except Exception as e:
        print(f"❌ 处理过程中出现错误：{e}")

def example_with_real_data():
    """
    使用真实数据的示例
    这个函数展示如何处理从影刀实际获取的数据
    """
    
    # 假设这是从影刀获取的真实数据
    # 在实际使用中，你需要将影刀返回的response_body_list传递给这个函数
    
    print("🔧 实际使用时的步骤：")
    print("1. 在影刀中配置API监控模式")
    print("2. 监控URL模式：*mtop.idle.web.xyh.item.list*")
    print("3. 获取response_body_list数据")
    print("4. 调用process_yingdao_response函数")
    print("5. 查看生成的Excel文件")
    
    print("\n📝 影刀脚本示例：")
    print("""
    // 在影刀中的JavaScript代码
    const responseData = await page.getApiResponses();
    const filteredData = responseData.filter(item => 
        item.url.includes('mtop.idle.web.xyh.item.list')
    );
    
    // 将filteredData传递给Python处理函数
    """)

if __name__ == "__main__":
    print("🚀 闲鱼数据分析工具 - 影刀数据处理示例")
    print("=" * 50)
    
    # 运行主示例
    main()
    
    print("\n" + "=" * 50)
    
    # 显示实际使用指导
    example_with_real_data()
