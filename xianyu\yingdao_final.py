#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
影刀最终调用脚本
这是影刀需要调用的最简化版本
"""

from xianyu_analyzer import process_yingdao_response

def main(response_body_list):
    """
    影刀调用的主函数
    
    参数:
    - response_body_list: 影刀API监控获取的数据
    
    使用方法:
    1. 在影刀中配置API监控：*mtop.idle.web.xyh.item.list*
    2. 获取response_body_list数据
    3. 调用此函数：main(response_body_list)
    """
    
    # 调用处理函数（所有配置已预设在代码中）
    result = process_yingdao_response(response_body_list)
    
    # 返回处理结果
    return {
        'success': result['success'],
        'count': result['count'],
        'excel_file': result['file'],
        'message': f"成功筛选出 {result['count']} 个热销商品，Excel文件：{result['file']}"
    }

# 如果直接运行此脚本
if __name__ == "__main__":
    print("📋 影刀调用说明：")
    print("1. 在影刀中配置API监控")
    print("2. URL模式：*mtop.idle.web.xyh.item.list*") 
    print("3. 获取response_body_list数据")
    print("4. 调用：main(response_body_list)")
    print("\n当前筛选条件（在xianyu_analyzer.py中预设）：")
    print("- 至少50人想要")
    print("- 价格100-20000元")
    print("- 热销排名前10名")
    print("- 72小时内发布")
    print("- 包含关键词：手机、iPhone、华为、小米、OPPO、vivo")
    print("- 排除关键词：坏了、故障、维修、翻新")
    print("\n💡 如需修改筛选条件，请编辑xianyu_analyzer.py文件")
