#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最简化的影刀数据处理示例
影刀只需要传递 response_body_list 数据即可
"""

from xianyu_analyzer import process_yingdao_response

def process_data(response_body_list):
    """
    影刀调用的主函数
    
    参数:
    - response_body_list: 影刀返回的API监控数据
    
    返回:
    - 处理结果
    """
    
    print("🚀 开始处理闲鱼数据...")
    
    # 直接调用处理函数（筛选条件和输出文件名已预设）
    result = process_yingdao_response(response_body_list)
    
    if result['success']:
        print(f"✅ 处理成功！")
        print(f"📊 筛选出 {result['count']} 个符合条件的商品")
        print(f"📁 Excel文件：{result['file']}")
        
        # 显示商品预览
        if result['products']:
            print(f"\n📋 商品预览：")
            for i, product in enumerate(result['products'][:3], 1):  # 只显示前3个
                print(f"{i}. {product['title']}")
                print(f"   💰 {product['price']}元 | ❤️ {product['want_count']}人想要 | 🔥 第{product['hot_rank']}名")
    else:
        print("❌ 处理失败")
    
    return result

# 影刀调用示例
if __name__ == "__main__":
    # 这里是模拟数据，实际使用时影刀会传递真实的response_body_list
    sample_data = []  # 影刀传递的实际数据
    
    print("📝 影刀使用说明：")
    print("1. 在影刀中配置API监控")
    print("2. URL模式：*mtop.idle.web.xyh.item.list*")
    print("3. 获取response_body_list数据")
    print("4. 调用：process_data(response_body_list)")
    print("\n" + "="*50)
    
    # 如果有数据就处理
    if sample_data:
        process_data(sample_data)
    else:
        print("⚠️  没有数据，请在影刀中获取response_body_list后调用process_data函数")
