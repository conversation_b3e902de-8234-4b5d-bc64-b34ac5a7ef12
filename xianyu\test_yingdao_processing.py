#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试影刀数据处理功能
"""

import json
from xianyu_analyzer import process_yingdao_response

def test_yingdao_processing():
    """测试影刀数据处理功能"""
    
    # 模拟影刀返回的数据结构（基于首页接口影刀.md的实际数据）
    sample_yingdao_response = [
        {
            "type": "XHR",
            "url": "https://h5api.m.taobao.com/h5/mtop.idle.web.xyh.item.list/1.0/?jsv=2.7.2&appKey=12574478&t=1735889745000&sign=xxx&api=mtop.idle.web.xyh.item.list&v=1.0&timeout=20000&AntiFlood=true&AntiCreep=true&H5Request=true&dataType=json&data=%7B%22categoryId%22%3A%22%22%2C%22sortType%22%3A%22_coefp%22%2C%22page%22%3A1%2C%22q%22%3A%22%E6%89%8B%E6%9C%BA%22%7D",
            "status": 200,
            "headers": {
                "content-type": "application/json;charset=UTF-8"
            },
            "body": json.dumps({
                "api": "mtop.idle.web.xyh.item.list",
                "data": {
                    "cardList": [
                        {
                            "cardType": 1003,
                            "cardData": {
                                "id": "123456789",
                                "title": "iPhone 15 Pro Max 256GB 深空黑色",
                                "priceInfo": {
                                    "price": "7999"
                                },
                                "detailUrl": "https://2.taobao.com/item.htm?id=123456789",
                                "picInfo": {
                                    "picUrl": "https://img.alicdn.com/imgextra/i1/123456789.jpg"
                                },
                                "categoryId": "50025707",
                                "trackParams": {
                                    "rankScore": "0.95",
                                    "user_id": "user123"
                                },
                                "itemLabelDataVO": {
                                    "labelData": {
                                        "r2": {
                                            "tagList": [
                                                {
                                                    "data": {
                                                        "content": "热销第1名",
                                                        "labelId": "1291"
                                                    }
                                                },
                                                {
                                                    "data": {
                                                        "content": "48小时内发布",
                                                        "labelId": "492"
                                                    }
                                                }
                                            ]
                                        },
                                        "r3": {
                                            "tagList": [
                                                {
                                                    "data": {
                                                        "content": "271人想要",
                                                        "labelId": "9"
                                                    }
                                                },
                                                {
                                                    "data": {
                                                        "content": "2人小刀价",
                                                        "labelId": "1047"
                                                    }
                                                }
                                            ]
                                        }
                                    }
                                }
                            }
                        },
                        {
                            "cardType": 1003,
                            "cardData": {
                                "id": "987654321",
                                "title": "华为Mate60 Pro 512GB 雅川青",
                                "priceInfo": {
                                    "price": "6299"
                                },
                                "detailUrl": "https://2.taobao.com/item.htm?id=987654321",
                                "picInfo": {
                                    "picUrl": "https://img.alicdn.com/imgextra/i2/987654321.jpg"
                                },
                                "categoryId": "50025707",
                                "trackParams": {
                                    "rankScore": "0.88",
                                    "user_id": "user456"
                                },
                                "itemLabelDataVO": {
                                    "labelData": {
                                        "r2": {
                                            "tagList": [
                                                {
                                                    "data": {
                                                        "content": "热销第3名",
                                                        "labelId": "1291"
                                                    }
                                                },
                                                {
                                                    "data": {
                                                        "content": "24小时内发布",
                                                        "labelId": "492"
                                                    }
                                                }
                                            ]
                                        },
                                        "r3": {
                                            "tagList": [
                                                {
                                                    "data": {
                                                        "content": "156人想要",
                                                        "labelId": "9"
                                                    }
                                                },
                                                {
                                                    "data": {
                                                        "content": "5人小刀价",
                                                        "labelId": "1047"
                                                    }
                                                }
                                            ]
                                        }
                                    }
                                }
                            }
                        }
                    ]
                }
            })
        }
    ]
    
    # 调用处理函数（筛选条件和输出文件名已在函数内预设）
    result = process_yingdao_response(response_body_list=sample_yingdao_response)
    
    print("=== 影刀数据处理测试结果 ===")
    print(f"处理状态: {result['success']}")
    print(f"筛选出商品数量: {result['count']}")
    print(f"Excel文件: {result['file']}")
    print("\n=== 商品预览 ===")
    
    for i, product in enumerate(result['products'], 1):
        print(f"\n商品 {i}:")
        print(f"  ID: {product['product_id']}")
        print(f"  标题: {product['title']}")
        print(f"  价格: {product['price']}元")
        print(f"  想要人数: {product['want_count']}")
        print(f"  小刀价人数: {product['bargain_count']}")
        print(f"  热销排名: {product['hot_rank']}")
        print(f"  发布时间范围: {product['publish_within_hours']}小时内")
        print(f"  排名分数: {product['rank_score']}")

if __name__ == "__main__":
    test_yingdao_processing()
