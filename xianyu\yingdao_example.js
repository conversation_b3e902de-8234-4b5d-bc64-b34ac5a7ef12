/**
 * 影刀脚本示例 - 闲鱼数据分析两步法
 * 配合 xianyu_analyzer.py 使用
 */

// 全局配置
const CONFIG = {
    SCROLL_WAIT_TIME: 2000,      // 滚动等待时间(毫秒)
    DETAIL_WAIT_TIME: 3000,      // 详情页加载等待时间(毫秒)
    CLICK_WAIT_TIME: 1000,       // 点击等待时间(毫秒)
    MAX_SCROLL_ATTEMPTS: 50,     // 最大滚动次数
    BATCH_SIZE: 10               // 批量处理详情页数量
};

// 筛选条件配置
const FILTERS = {
    // 第一步筛选条件
    STEP1: {
        want_count_min: 5,       // 至少5人想要
        price_max: 100,          // 价格不超过100元
        price_min: 1,            // 价格不低于1元
        keywords: ["教材", "英语"], // 包含关键词
        exclude_keywords: ["二手", "破损"] // 排除关键词
    },
    
    // 第二步筛选条件
    STEP2: {
        publish_hours: 24,       // 24小时内发布
        want_count_min: 5,       // 至少5人想要
        price_max: 100,          // 价格不超过100元
        keywords: ["教材", "英语"]
    }
};

/**
 * 主函数：分析闲鱼商品数据
 */
function analyzeXianyuProducts() {
    try {
        console.log("=== 开始闲鱼数据分析 ===");
        
        // 第一步：获取商品列表并初筛
        console.log("第一步：获取商品列表数据...");
        let productListData = getProductListData();
        
        if (!productListData || productListData.length === 0) {
            throw new Error("未获取到商品列表数据");
        }
        
        console.log(`获取到 ${productListData.length} 批商品列表数据`);
        
        // 调用Python函数进行初筛
        let targetProductIds = callPythonFunction('filter_product_list', {
            data: productListData,
            filters: FILTERS.STEP1
        });
        
        console.log(`初筛完成，找到 ${targetProductIds.length} 个目标商品`);
        
        if (targetProductIds.length === 0) {
            console.log("没有符合条件的商品，分析结束");
            return { success: true, count: 0, message: "没有符合条件的商品" };
        }
        
        // 第二步：获取商品详情并最终处理
        console.log("第二步：获取商品详情数据...");
        let detailDataList = getProductDetailsData(targetProductIds);
        
        if (!detailDataList || detailDataList.length === 0) {
            throw new Error("未获取到商品详情数据");
        }
        
        console.log(`获取到 ${detailDataList.length} 个商品详情数据`);
        
        // 调用Python函数进行最终处理
        let outputFileName = `闲鱼热销产品_${getCurrentDateString()}.xlsx`;
        let result = callPythonFunction('process_product_details', {
            data: detailDataList,
            filters: FILTERS.STEP2,
            output_file: outputFileName
        });
        
        console.log("=== 分析完成 ===");
        console.log(`共找到 ${result.count} 个符合条件的商品`);
        console.log(`Excel文件已保存：${result.file}`);
        
        return result;
        
    } catch (error) {
        console.error("分析过程出错：", error.message);
        return { 
            success: false, 
            error: error.message,
            count: 0
        };
    }
}

/**
 * 第一步：获取商品列表数据
 */
function getProductListData() {
    let productListData = [];
    let scrollAttempts = 0;
    let hasMoreData = true;
    
    console.log("开始滚动获取商品列表...");
    
    while (hasMoreData && scrollAttempts < CONFIG.MAX_SCROLL_ATTEMPTS) {
        try {
            // 滚动到页面底部
            scrollToBottom();
            wait(CONFIG.SCROLL_WAIT_TIME);
            
            // 拦截API响应 - 尝试多种可能的接口名称
            let apiResponse = interceptApiResponse('mtop.idle.web.xyh.item.list') ||
                            interceptApiResponse('mtop.idle.web.xyh.item.list.2.0') ||
                            interceptApiResponse('mtop.taobao.idle.home.list') ||
                            interceptApiResponse('item.list');
            
            if (apiResponse && apiResponse.data && apiResponse.data.cardList) {
                productListData.push(apiResponse);
                console.log(`第 ${scrollAttempts + 1} 次滚动，获取到 ${apiResponse.data.cardList.length} 个商品`);
                
                // 检查是否还有更多数据
                hasMoreData = checkHasMoreData(apiResponse);
            } else {
                console.log(`第 ${scrollAttempts + 1} 次滚动，未获取到有效数据`);
                hasMoreData = false;
            }
            
            scrollAttempts++;
            
        } catch (error) {
            console.error(`第 ${scrollAttempts + 1} 次滚动出错：`, error.message);
            scrollAttempts++;
        }
    }
    
    console.log(`滚动结束，共获取 ${productListData.length} 批数据`);
    return productListData;
}

/**
 * 第二步：获取商品详情数据
 */
function getProductDetailsData(productIds) {
    let detailDataList = [];
    let processedCount = 0;
    
    console.log(`开始获取 ${productIds.length} 个商品的详情数据...`);
    
    for (let i = 0; i < productIds.length; i++) {
        let productId = productIds[i];
        
        try {
            console.log(`处理第 ${i + 1}/${productIds.length} 个商品: ${productId}`);
            
            // 点击商品进入详情页
            let clickSuccess = clickProductById(productId);
            if (!clickSuccess) {
                console.warn(`点击商品 ${productId} 失败，跳过`);
                continue;
            }
            
            wait(CONFIG.DETAIL_WAIT_TIME);
            
            // 拦截详情页API响应
            let detailResponse = interceptApiResponse('mtop.taobao.idle.item.web.recommend.list');
            
            if (detailResponse && detailResponse.data) {
                detailDataList.push({
                    productId: productId,
                    detailData: detailResponse
                });
                processedCount++;
                console.log(`成功获取商品 ${productId} 的详情数据`);
            } else {
                console.warn(`获取商品 ${productId} 详情数据失败`);
            }
            
            // 关闭详情页，返回列表页
            closeCurrentTab();
            wait(CONFIG.CLICK_WAIT_TIME);
            
            // 批量处理进度提示
            if ((i + 1) % CONFIG.BATCH_SIZE === 0) {
                console.log(`已处理 ${i + 1}/${productIds.length} 个商品，成功获取 ${processedCount} 个详情`);
            }
            
        } catch (error) {
            console.error(`处理商品 ${productId} 详情时出错：`, error.message);
            
            // 尝试关闭可能打开的详情页
            try {
                closeCurrentTab();
            } catch (e) {
                // 忽略关闭标签页的错误
            }
            
            wait(CONFIG.CLICK_WAIT_TIME);
        }
    }
    
    console.log(`详情获取完成，成功获取 ${processedCount}/${productIds.length} 个商品详情`);
    return detailDataList;
}

/**
 * 辅助函数：检查是否还有更多数据
 */
function checkHasMoreData(apiResponse) {
    // 根据实际API响应结构判断是否还有更多数据
    // 这里需要根据实际情况调整判断逻辑
    
    let cardList = apiResponse.data.cardList;
    if (!cardList || cardList.length === 0) {
        return false;
    }
    
    // 如果返回的商品数量少于预期，可能已经到底了
    if (cardList.length < 10) {
        return false;
    }
    
    return true;
}

/**
 * 辅助函数：根据商品ID点击商品
 */
function clickProductById(productId) {
    try {
        // 查找包含指定商品ID的商品元素
        let productElement = findElementByProductId(productId);
        
        if (productElement) {
            productElement.click();
            return true;
        } else {
            console.warn(`未找到商品ID为 ${productId} 的元素`);
            return false;
        }
    } catch (error) {
        console.error(`点击商品 ${productId} 失败：`, error.message);
        return false;
    }
}

/**
 * 辅助函数：查找商品元素
 */
function findElementByProductId(productId) {
    // 这里需要根据实际页面结构来实现
    // 示例：通过data-id属性或href中包含商品ID来查找
    
    // 方法1：通过data-id属性查找
    let element = document.querySelector(`[data-id="${productId}"]`);
    if (element) return element;
    
    // 方法2：通过href包含商品ID的链接查找
    let linkElement = document.querySelector(`a[href*="${productId}"]`);
    if (linkElement) return linkElement;
    
    // 方法3：通过文本内容查找（不推荐，但作为备选）
    let allLinks = document.querySelectorAll('a');
    for (let link of allLinks) {
        if (link.href && link.href.includes(productId)) {
            return link;
        }
    }
    
    return null;
}

/**
 * 辅助函数：获取当前日期字符串
 */
function getCurrentDateString() {
    let now = new Date();
    let year = now.getFullYear();
    let month = String(now.getMonth() + 1).padStart(2, '0');
    let day = String(now.getDate()).padStart(2, '0');
    let hour = String(now.getHours()).padStart(2, '0');
    let minute = String(now.getMinutes()).padStart(2, '0');
    
    return `${year}${month}${day}_${hour}${minute}`;
}

/**
 * 辅助函数：滚动到页面底部
 */
function scrollToBottom() {
    window.scrollTo(0, document.body.scrollHeight);
}

/**
 * 辅助函数：关闭当前标签页
 */
function closeCurrentTab() {
    // 如果是在新标签页中打开的详情页，关闭当前标签页
    if (window.history.length > 1) {
        window.history.back();
    } else {
        window.close();
    }
}

/**
 * 辅助函数：等待指定时间
 */
function wait(milliseconds) {
    return new Promise(resolve => setTimeout(resolve, milliseconds));
}

// 导出主函数供影刀调用
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        analyzeXianyuProducts,
        getProductListData,
        getProductDetailsData,
        CONFIG,
        FILTERS
    };
}
