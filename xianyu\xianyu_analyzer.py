#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
闲鱼数据分析工具 - 两步法实现
作者: AI Assistant
功能: 配合影刀RPA，分析闲鱼商品数据并导出Excel
"""

import json
import re
import pandas as pd
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional
import logging

# 配置日志
def setup_logger(log_file=None):
    """设置日志配置"""
    logger = logging.getLogger(__name__)
    logger.setLevel(logging.INFO)

    # 清除现有的处理器
    for handler in logger.handlers[:]:
        logger.removeHandler(handler)

    # 创建格式器
    formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')

    # 控制台处理器
    console_handler = logging.StreamHandler()
    console_handler.setFormatter(formatter)
    logger.addHandler(console_handler)

    # 文件处理器（如果指定了日志文件）
    if log_file:
        file_handler = logging.FileHandler(log_file, encoding='utf-8')
        file_handler.setFormatter(formatter)
        logger.addHandler(file_handler)

    return logger

# 初始化日志
logger = setup_logger()


class XianyuAnalyzer:
    """闲鱼数据分析器"""
    
    def __init__(self):
        self.processed_count = 0
        self.filtered_count = 0

    def process_yingdao_response(self, response_body_list: List[Dict], filters: Optional[Dict] = None,
                                output_file: Optional[str] = None) -> Dict[str, Any]:
        """
        处理影刀返回的response_body_list数据并直接导出Excel

        参数:
        - response_body_list: 影刀返回的响应体列表
        - filters: 筛选条件
        - output_file: 输出文件名

        返回:
        - 处理结果信息
        """
        if not filters:
            filters = {}

        # 设置日志文件（与Excel文件同目录）
        if output_file:
            log_file = output_file.replace('.xlsx', '.log')
            global logger
            logger = setup_logger(log_file)

        logger.info("="*60)
        logger.info("开始处理影刀返回的数据")
        logger.info(f"接收到 {len(response_body_list)} 个响应体")
        logger.info(f"筛选条件: {filters}")

        processed_products = []
        total_cards = 0
        valid_products = 0

        for idx, response_item in enumerate(response_body_list):
            logger.info(f"处理第 {idx+1} 个响应体...")
            try:
                # 检查响应体结构
                if not isinstance(response_item, dict):
                    logger.warning(f"响应体 {idx+1} 不是字典格式，跳过")
                    continue

                logger.info(f"响应体 {idx+1} 类型: {response_item.get('type', 'unknown')}")
                logger.info(f"响应体 {idx+1} URL: {response_item.get('url', 'unknown')[:100]}...")
                logger.info(f"响应体 {idx+1} 状态: {response_item.get('status', 'unknown')}")

                # 解析响应体中的JSON数据
                body_data = response_item.get('body')
                if not body_data:
                    logger.warning(f"响应体 {idx+1} 没有body数据，跳过")
                    continue

                if isinstance(body_data, str):
                    import json
                    try:
                        body_data = json.loads(body_data)
                        logger.info(f"响应体 {idx+1} JSON解析成功")
                    except json.JSONDecodeError as e:
                        logger.error(f"响应体 {idx+1} JSON解析失败: {e}")
                        continue

                # 提取商品列表数据
                data_section = body_data.get('data', {})
                if not data_section:
                    logger.warning(f"响应体 {idx+1} 没有data字段，跳过")
                    continue

                card_list = data_section.get('cardList', [])
                logger.info(f"响应体 {idx+1} 包含 {len(card_list)} 个卡片")
                total_cards += len(card_list)

                for card_idx, card_item in enumerate(card_list):
                    try:
                        card_type = card_item.get('cardType')
                        logger.debug(f"卡片 {card_idx+1} 类型: {card_type}")

                        # 跳过非商品卡片
                        if card_type != 1003:
                            logger.debug(f"卡片 {card_idx+1} 不是商品卡片，跳过")
                            continue

                        card_data = card_item.get('cardData', {})
                        if not card_data:
                            logger.warning(f"卡片 {card_idx+1} 没有cardData，跳过")
                            continue

                        product_info = self._parse_yingdao_product(card_data)
                        if not product_info:
                            logger.warning(f"卡片 {card_idx+1} 解析失败，跳过")
                            continue

                        valid_products += 1
                        logger.info(f"解析商品: {product_info['title'][:30]}... | 价格: {product_info['price']}元 | 想要: {product_info['want_count']}人")

                        if self._apply_yingdao_filters(product_info, filters):
                            processed_products.append(product_info)
                            logger.info(f"✅ 商品通过筛选: {product_info['title'][:30]}...")
                        else:
                            logger.debug(f"❌ 商品未通过筛选: {product_info['title'][:30]}...")

                    except Exception as e:
                        logger.error(f"处理卡片 {card_idx+1} 失败: {e}")
                        continue

            except Exception as e:
                logger.error(f"解析响应体 {idx+1} 失败: {e}")
                continue

        self.filtered_count = len(processed_products)
        logger.info("="*60)
        logger.info(f"数据处理统计:")
        logger.info(f"- 总响应体数量: {len(response_body_list)}")
        logger.info(f"- 总卡片数量: {total_cards}")
        logger.info(f"- 有效商品数量: {valid_products}")
        logger.info(f"- 筛选通过数量: {self.filtered_count}")

        # 导出Excel
        excel_file = None
        if output_file and processed_products:
            logger.info(f"开始导出Excel文件: {output_file}")
            excel_file = self._export_to_excel(processed_products, output_file)
            logger.info(f"✅ Excel文件导出成功: {excel_file}")
        elif output_file and not processed_products:
            logger.warning("没有符合条件的商品，跳过Excel导出")

        logger.info("="*60)

        return {
            'success': True,
            'count': self.filtered_count,
            'file': excel_file,
            'products': processed_products[:10]  # 只返回前10个商品作为预览
        }

    def _parse_yingdao_product(self, card_data: Dict) -> Optional[Dict[str, Any]]:
        """解析影刀返回的商品数据"""
        try:
            product_info = {
                'product_id': card_data.get('id', ''),
                'title': card_data.get('title', ''),
                'price': float(card_data.get('priceInfo', {}).get('price', 0)),
                'detail_url': card_data.get('detailUrl', ''),
                'image_url': card_data.get('picInfo', {}).get('picUrl', ''),
                'category_id': card_data.get('categoryId', ''),
                'rank_score': float(card_data.get('trackParams', {}).get('rankScore', 0)),
                'user_id': card_data.get('trackParams', {}).get('user_id', ''),
                'want_count': 0,
                'bargain_count': 0,
                'hot_rank': None,
                'publish_within_hours': None  # 从标签中解析
            }

            # 解析标签信息
            label_data = card_data.get('itemLabelDataVO', {}).get('labelData', {})
            labels = self._extract_yingdao_labels(label_data)
            product_info.update(labels)

            return product_info

        except Exception as e:
            logger.error(f"解析影刀商品数据失败: {e}")
            return None

    def _extract_yingdao_labels(self, label_data: Dict) -> Dict[str, Any]:
        """提取影刀数据中的标签信息"""
        result = {
            'want_count': 0,
            'bargain_count': 0,
            'hot_rank': None,
            'publish_within_hours': None
        }

        # 解析r3区域标签（想要人数、小刀价）
        r3_data = label_data.get('r3', {})
        if isinstance(r3_data, dict) and 'tagList' in r3_data:
            for tag in r3_data['tagList']:
                content = tag.get('data', {}).get('content', '')
                label_id = tag.get('data', {}).get('labelId', '')

                if label_id == '9' and '人想要' in content:
                    match = re.search(r'(\d+)人想要', content)
                    if match:
                        result['want_count'] = int(match.group(1))

                elif label_id == '1047' and '人小刀价' in content:
                    match = re.search(r'(\d+)人小刀价', content)
                    if match:
                        result['bargain_count'] = int(match.group(1))

        # 解析r2区域标签（热销排名、发布时间）
        r2_data = label_data.get('r2', {})
        if isinstance(r2_data, dict) and 'tagList' in r2_data:
            for tag in r2_data['tagList']:
                content = tag.get('data', {}).get('content', '')
                label_id = tag.get('data', {}).get('labelId', '')

                if label_id == '1291' and '热销第' in content and '名' in content:
                    match = re.search(r'热销第(\d+)名', content)
                    if match:
                        result['hot_rank'] = int(match.group(1))

                elif label_id == '492' and '小时内发布' in content:
                    match = re.search(r'(\d+)小时内发布', content)
                    if match:
                        result['publish_within_hours'] = int(match.group(1))

        return result

    def _apply_yingdao_filters(self, product_info: Dict, filters: Dict) -> bool:
        """应用影刀数据的筛选条件"""
        # 想要人数筛选
        if product_info.get('want_count', 0) < filters.get('want_count_min', 0):
            return False

        # 价格筛选
        price = product_info.get('price', 0)
        if price < filters.get('price_min', 0) or price > filters.get('price_max', float('inf')):
            return False

        # 热销排名筛选
        hot_rank = product_info.get('hot_rank')
        if filters.get('hot_rank_max') and (not hot_rank or hot_rank > filters['hot_rank_max']):
            return False

        # 发布时间筛选（基于标签）
        publish_hours = product_info.get('publish_within_hours')
        if filters.get('publish_hours_max') and (not publish_hours or publish_hours > filters['publish_hours_max']):
            return False

        # 关键词筛选
        title_lower = product_info.get('title', '').lower()
        if filters.get('keywords'):
            if not any(keyword.lower() in title_lower for keyword in filters['keywords']):
                return False

        if filters.get('exclude_keywords'):
            if any(keyword.lower() in title_lower for keyword in filters['exclude_keywords']):
                return False

        return True

    def filter_product_list(self, data: List[Dict], filters: Optional[Dict] = None) -> List[str]:
        """
        第一步：筛选商品列表，返回需要获取详情的商品ID
        
        参数:
        - data: 商品列表API响应数据
        - filters: 初筛条件
        
        返回:
        - 商品ID列表
        """
        if not filters:
            filters = {}
        
        logger.info("开始第一步：商品列表初筛")
        target_product_ids = []
        total_products = 0
        
        for api_response in data:
            if not isinstance(api_response, dict) or not api_response.get('data', {}).get('cardList'):
                continue
                
            for card_item in api_response['data']['cardList']:
                card_data = card_item.get('cardData', {})
                total_products += 1
                
                # 解析基础信息
                product_id = card_data.get('id')
                if not product_id:
                    continue
                    
                title = card_data.get('title', '')
                price_info = card_data.get('priceInfo', {})
                price = float(price_info.get('price', 0)) if price_info.get('price') else 0
                
                # 解析标签信息
                label_data = card_data.get('itemLabelDataVO', {}).get('labelData', {})
                labels = self._extract_list_page_labels(label_data)
                want_count = labels.get('want_count', 0)
                
                # 应用筛选条件
                if self._apply_list_filters(title, price, want_count, labels, filters):
                    target_product_ids.append(product_id)
        
        logger.info(f"第一步完成：从 {total_products} 个商品中筛选出 {len(target_product_ids)} 个目标商品")
        return target_product_ids
    
    def process_product_details(self, data: List[Dict], filters: Optional[Dict] = None, 
                              output_file: Optional[str] = None) -> Dict[str, Any]:
        """
        第二步：处理商品详情数据并导出Excel
        
        参数:
        - data: 商品详情API响应数据列表
        - filters: 精筛条件
        - output_file: 输出文件名
        
        返回:
        - 处理结果信息
        """
        if not filters:
            filters = {}
        
        logger.info("开始第二步：商品详情精筛")
        processed_products = []
        
        for item in data:
            product_id = item.get('productId')
            detail_data = item.get('detailData', {})
            
            if not detail_data.get('data', {}).get('cardList'):
                continue
            
            # 解析详情页数据
            for card_item in detail_data['data']['cardList']:
                card_data = card_item.get('cardData', {})
                
                # 跳过非商品卡片（如推荐标题卡片）
                if card_item.get('cardType') != 100005:
                    continue
                
                product_info = self._parse_detail_product(card_data)
                
                if product_info and self._apply_detail_filters(product_info, filters):
                    processed_products.append(product_info)
        
        self.filtered_count = len(processed_products)
        logger.info(f"第二步完成：筛选出 {self.filtered_count} 个符合条件的商品")
        
        # 导出Excel
        excel_file = None
        if output_file and processed_products:
            excel_file = self._export_to_excel(processed_products, output_file)
            logger.info(f"Excel文件已导出：{excel_file}")
        
        return {
            'success': True,
            'count': self.filtered_count,
            'file': excel_file,
            'products': processed_products[:10]  # 只返回前10个商品作为预览
        }
    
    def _extract_list_page_labels(self, label_data: Dict) -> Dict[str, Any]:
        """提取列表页标签信息"""
        result = {
            'want_count': 0,
            'bargain_count': 0,
            'hot_rank': None
        }
        
        # 解析r3区域标签（想要人数、小刀价）
        r3_labels = label_data.get('r3', [])
        for label in r3_labels:
            content = label.get('content', '')
            label_id = label.get('labelId')
            
            if label_id == 9 and '人想要' in content:
                match = re.search(r'(\d+)人想要', content)
                if match:
                    result['want_count'] = int(match.group(1))
            
            elif label_id == 1047 and '人小刀价' in content:
                match = re.search(r'(\d+)人小刀价', content)
                if match:
                    result['bargain_count'] = int(match.group(1))
        
        # 解析r2区域标签（热销排名）
        r2_labels = label_data.get('r2', [])
        for label in r2_labels:
            content = label.get('content', '')
            if '热销第' in content and '名' in content:
                match = re.search(r'热销第(\d+)名', content)
                if match:
                    result['hot_rank'] = int(match.group(1))
        
        return result
    
    def _extract_detail_page_labels(self, fish_tags: Dict) -> Dict[str, Any]:
        """提取详情页fishTags标签信息"""
        result = {
            'want_count': 0,
            'bargain_count': 0,
            'hot_rank': None
        }
        
        # 解析r3区域标签（想要人数、小刀价）
        r3_tags = fish_tags.get('r3', {}).get('tagList', [])
        for tag in r3_tags:
            content = tag.get('data', {}).get('content', '')
            label_id = tag.get('data', {}).get('labelId', '')
            
            if label_id == '9' and '人想要' in content:
                match = re.search(r'(\d+)人想要', content)
                if match:
                    result['want_count'] = int(match.group(1))
            
            elif label_id == '1047' and '人小刀价' in content:
                match = re.search(r'(\d+)人小刀价', content)
                if match:
                    result['bargain_count'] = int(match.group(1))
        
        # 解析r2区域标签（热销排名）
        r2_tags = fish_tags.get('r2', {}).get('tagList', [])
        for tag in r2_tags:
            content = tag.get('data', {}).get('content', '')
            if '热销第' in content and '名' in content:
                match = re.search(r'热销第(\d+)名', content)
                if match:
                    result['hot_rank'] = int(match.group(1))
        
        return result
    
    def _parse_detail_product(self, card_data: Dict) -> Optional[Dict[str, Any]]:
        """解析详情页商品数据"""
        try:
            click_param = card_data.get('clickParam', {}).get('args', {})
            
            product_info = {
                'product_id': card_data.get('itemId', ''),
                'title': card_data.get('title', ''),
                'price': float(card_data.get('price', 0)),
                'publish_time': click_param.get('publishTime'),
                'shop_name': card_data.get('user', {}).get('userNick', ''),
                'description': card_data.get('desc', ''),
                'area': card_data.get('area', ''),
                'target_url': card_data.get('targetUrl', ''),
                'image_url': card_data.get('image', {}).get('url', ''),
                'user_avatar': card_data.get('user', {}).get('avatar', '')
            }
            
            # 解析标签信息
            fish_tags = card_data.get('fishTags', {})
            labels = self._extract_detail_page_labels(fish_tags)
            product_info.update(labels)
            
            return product_info
            
        except Exception as e:
            logger.error(f"解析商品数据失败: {e}")
            return None
    
    def _apply_list_filters(self, title: str, price: float, want_count: int, 
                           labels: Dict, filters: Dict) -> bool:
        """应用列表页筛选条件"""
        # 想要人数筛选
        if want_count < filters.get('want_count_min', 0):
            return False
        
        # 价格筛选
        if price < filters.get('price_min', 0) or price > filters.get('price_max', float('inf')):
            return False
        
        # 热销排名筛选
        hot_rank = labels.get('hot_rank')
        if filters.get('hot_rank_max') and (not hot_rank or hot_rank > filters['hot_rank_max']):
            return False
        
        # 关键词筛选
        title_lower = title.lower()
        if filters.get('keywords'):
            if not any(keyword.lower() in title_lower for keyword in filters['keywords']):
                return False
        
        if filters.get('exclude_keywords'):
            if any(keyword.lower() in title_lower for keyword in filters['exclude_keywords']):
                return False
        
        return True
    
    def _apply_detail_filters(self, product_info: Dict, filters: Dict) -> bool:
        """应用详情页筛选条件"""
        # 发布时间筛选
        if filters.get('publish_hours'):
            publish_time_ms = product_info.get('publish_time')
            if publish_time_ms:
                if not self._filter_by_publish_time(int(publish_time_ms), filters['publish_hours']):
                    return False
        
        # 其他筛选条件
        return self._apply_list_filters(
            product_info.get('title', ''),
            product_info.get('price', 0),
            product_info.get('want_count', 0),
            product_info,
            filters
        )
    
    def _filter_by_publish_time(self, publish_time_ms: int, hours_limit: int) -> bool:
        """根据发布时间筛选"""
        try:
            publish_time = datetime.fromtimestamp(publish_time_ms / 1000)
            current_time = datetime.now()
            time_diff = current_time - publish_time

            return time_diff.total_seconds() <= hours_limit * 3600
        except Exception as e:
            logger.error(f"时间筛选失败: {e}")
            return False

    def _export_to_excel(self, products: List[Dict], filename: str) -> str:
        """导出数据到Excel"""
        try:
            # 转换数据格式
            excel_data = []
            for product in products:
                # 转换发布时间
                publish_time = ''
                if product.get('publish_time'):
                    try:
                        timestamp = int(product['publish_time']) / 1000
                        publish_time = datetime.fromtimestamp(timestamp).strftime('%Y-%m-%d %H:%M:%S')
                    except:
                        publish_time = '时间解析失败'

                # 处理发布时间范围（影刀数据特有）
                publish_time_range = ''
                if product.get('publish_within_hours'):
                    publish_time_range = f"{product.get('publish_within_hours')}小时内发布"

                excel_data.append({
                    '商品ID': product.get('product_id', ''),
                    '商品标题': product.get('title', ''),
                    '商品价格': product.get('price', 0),
                    '发布时间': publish_time,
                    '发布时间范围': publish_time_range,
                    '店铺名': product.get('shop_name', ''),
                    '地区': product.get('area', ''),
                    '想要人数': product.get('want_count', 0),
                    '小刀价人数': product.get('bargain_count', 0),
                    '热销排名': product.get('hot_rank', ''),
                    '商品描述': product.get('description', '')[:100] + '...' if len(product.get('description', '')) > 100 else product.get('description', ''),
                    '商品链接': product.get('detail_url', '') or product.get('target_url', ''),
                    '主图URL': product.get('image_url', ''),
                    '排名分数': product.get('rank_score', 0)
                })

            # 创建DataFrame并导出
            df = pd.DataFrame(excel_data)

            with pd.ExcelWriter(filename, engine='openpyxl') as writer:
                df.to_excel(writer, sheet_name='闲鱼热销产品', index=False)

                # 格式化工作表
                worksheet = writer.sheets['闲鱼热销产品']

                # 调整列宽
                column_widths = {
                    'A': 15,  # 商品ID
                    'B': 30,  # 商品标题
                    'C': 10,  # 商品价格
                    'D': 20,  # 发布时间
                    'E': 15,  # 店铺名
                    'F': 10,  # 地区
                    'G': 10,  # 想要人数
                    'H': 12,  # 小刀价人数
                    'I': 10,  # 热销排名
                    'J': 50,  # 商品描述
                    'K': 40,  # 商品链接
                    'L': 40   # 主图URL
                }

                for col, width in column_widths.items():
                    worksheet.column_dimensions[col].width = width

            logger.info(f"Excel文件导出成功：{filename}")
            return filename

        except Exception as e:
            logger.error(f"Excel导出失败: {e}")
            return None


# 影刀调用的主要函数
def filter_product_list(data, filters=None):
    """影刀调用 - 第一步：筛选商品列表"""
    analyzer = XianyuAnalyzer()
    return analyzer.filter_product_list(data, filters)


def process_product_details(data, filters=None, output_file=None):
    """影刀调用 - 第二步：处理商品详情"""
    analyzer = XianyuAnalyzer()
    return analyzer.process_product_details(data, filters, output_file)


def process_yingdao_response(response_body_list):
    """
    影刀调用 - 处理影刀返回的response_body_list数据并直接导出Excel

    参数:
    - response_body_list: 影刀返回的响应体列表

    返回:
    - 处理结果信息
    """
    # 预设筛选条件
    filters = {
        'want_count_min': 50,        # 至少50人想要
        'price_min': 100,            # 最低价格100元
        'price_max': 20000,          # 最高价格20000元
        'hot_rank_max': 10,          # 热销排名前10名
        'publish_hours_max': 72,     # 72小时内发布
        'keywords': ['手机', 'iPhone', '华为', '小米', 'OPPO', 'vivo'],  # 包含关键词
        'exclude_keywords': ['坏了', '故障', '维修', '翻新']  # 排除关键词
    }

    # 预设输出文件名（带时间戳）
    from datetime import datetime
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    output_file = f'闲鱼热销商品_{timestamp}.xlsx'

    analyzer = XianyuAnalyzer()
    return analyzer.process_yingdao_response(response_body_list, filters, output_file)


# 测试函数
def test_analyzer():
    """测试分析器功能"""
    # 模拟测试数据
    test_list_data = [
        {
            "api": "mtop.idle.web.xyh.item.list",
            "data": {
                "cardList": [
                    {
                        "cardData": {
                            "id": "123456789",
                            "title": "英语教材测试商品",
                            "priceInfo": {"price": "50"},
                            "itemLabelDataVO": {
                                "labelData": {
                                    "r3": [
                                        {"content": "10人想要", "labelId": 9}
                                    ]
                                }
                            }
                        }
                    }
                ]
            }
        }
    ]

    # 测试第一步
    target_ids = filter_product_list(test_list_data, {
        "want_count_min": 5,
        "price_max": 100
    })

    print(f"第一步测试结果：{target_ids}")

    # 模拟详情页数据
    test_detail_data = [
        {
            "productId": "123456789",
            "detailData": {
                "data": {
                    "cardList": [
                        {
                            "cardType": 100005,
                            "cardData": {
                                "itemId": "123456789",
                                "title": "英语教材测试商品",
                                "price": "50",
                                "clickParam": {
                                    "args": {
                                        "publishTime": str(int(datetime.now().timestamp() * 1000))
                                    }
                                },
                                "user": {"userNick": "测试店铺"},
                                "area": "北京",
                                "desc": "这是一个测试商品描述",
                                "targetUrl": "https://test.com",
                                "image": {"url": "https://test.jpg"},
                                "fishTags": {
                                    "r3": {
                                        "tagList": [
                                            {
                                                "data": {
                                                    "content": "10人想要",
                                                    "labelId": "9"
                                                }
                                            }
                                        ]
                                    }
                                }
                            }
                        }
                    ]
                }
            }
        }
    ]

    # 测试第二步
    result = process_product_details(test_detail_data, {
        "publish_hours": 24,
        "want_count_min": 5
    }, "test_output.xlsx")

    print(f"第二步测试结果：{result}")


if __name__ == "__main__":
    test_analyzer()
